package com.youying.web.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.youying.common.utils.StringUtils;
import com.youying.system.domain.repertoire.RepertoireCacheInfo;

import lombok.extern.slf4j.Slf4j;

/**
 * 剧目匹配性能测试
 * 
 * <AUTHOR>
 * @since 2025-01-07
 */
@SpringBootTest
@Slf4j
public class RepertoireMatchPerformanceTest {
    
    private List<RepertoireCacheInfo> testRepertoireList;
    private RepertoireMatchOptimizer optimizer;
    private List<String> testQueries;
    
    @BeforeEach
    public void setUp() {
        // 创建7000条测试数据
        testRepertoireList = createTestRepertoireList(7000);
        optimizer = new RepertoireMatchOptimizer();
        
        // 创建测试查询
        testQueries = createTestQueries();
        
        log.info("测试数据准备完成，剧目数量: {}, 测试查询数量: {}", 
                testRepertoireList.size(), testQueries.size());
    }
    
    @Test
    public void testOriginalMatchPerformance() {
        log.info("=== 原始匹配算法性能测试 ===");
        
        long totalTime = 0;
        int totalMatches = 0;
        
        for (String query : testQueries) {
            long startTime = System.currentTimeMillis();
            
            // 模拟原始算法
            List<Long> matches = performOriginalMatch(query);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            totalTime += duration;
            totalMatches += matches.size();
            
            log.info("查询: '{}', 耗时: {}ms, 匹配数量: {}", query, duration, matches.size());
        }
        
        log.info("原始算法总耗时: {}ms, 平均耗时: {}ms, 总匹配数量: {}", 
                totalTime, totalTime / testQueries.size(), totalMatches);
    }
    
    @Test
    public void testOptimizedMatchPerformance() {
        log.info("=== 优化匹配算法性能测试 ===");
        
        long totalTime = 0;
        int totalMatches = 0;
        
        for (String query : testQueries) {
            long startTime = System.currentTimeMillis();
            
            // 使用优化算法
            List<Long> matches = optimizer.findMatchingRepertoires(query, testRepertoireList);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            totalTime += duration;
            totalMatches += (matches != null ? matches.size() : 0);
            
            log.info("查询: '{}', 耗时: {}ms, 匹配数量: {}", query, duration, 
                    matches != null ? matches.size() : 0);
        }
        
        log.info("优化算法总耗时: {}ms, 平均耗时: {}ms, 总匹配数量: {}", 
                totalTime, totalTime / testQueries.size(), totalMatches);
    }
    
    @Test
    public void testPerformanceComparison() {
        log.info("=== 性能对比测试 ===");
        
        // 预热
        for (String query : testQueries.subList(0, Math.min(5, testQueries.size()))) {
            performOriginalMatch(query);
            optimizer.findMatchingRepertoires(query, testRepertoireList);
        }
        
        // 原始算法测试
        long originalStartTime = System.currentTimeMillis();
        int originalMatches = 0;
        for (String query : testQueries) {
            List<Long> matches = performOriginalMatch(query);
            originalMatches += matches.size();
        }
        long originalEndTime = System.currentTimeMillis();
        long originalTotalTime = originalEndTime - originalStartTime;
        
        // 优化算法测试
        long optimizedStartTime = System.currentTimeMillis();
        int optimizedMatches = 0;
        for (String query : testQueries) {
            List<Long> matches = optimizer.findMatchingRepertoires(query, testRepertoireList);
            optimizedMatches += (matches != null ? matches.size() : 0);
        }
        long optimizedEndTime = System.currentTimeMillis();
        long optimizedTotalTime = optimizedEndTime - optimizedStartTime;
        
        // 输出对比结果
        log.info("=== 性能对比结果 ===");
        log.info("原始算法 - 总耗时: {}ms, 平均耗时: {}ms, 匹配数量: {}", 
                originalTotalTime, originalTotalTime / testQueries.size(), originalMatches);
        log.info("优化算法 - 总耗时: {}ms, 平均耗时: {}ms, 匹配数量: {}", 
                optimizedTotalTime, optimizedTotalTime / testQueries.size(), optimizedMatches);
        
        double speedup = (double) originalTotalTime / optimizedTotalTime;
        log.info("性能提升倍数: {:.2f}x", speedup);
        log.info("性能提升百分比: {:.1f}%", (speedup - 1) * 100);
    }
    
    /**
     * 模拟原始匹配算法
     */
    private List<Long> performOriginalMatch(String imagesStr) {
        List<Long> correspondedRepertoires = new ArrayList<>();
        
        for (RepertoireCacheInfo cacheInfo : testRepertoireList) {
            double matchDegree = StringUtils.matchDegree(cacheInfo.getName(), imagesStr);
            if (matchDegree >= 0.8) {
                correspondedRepertoires.add(cacheInfo.getId());
            }
        }
        
        return correspondedRepertoires;
    }
    
    /**
     * 创建测试剧目列表
     */
    private List<RepertoireCacheInfo> createTestRepertoireList(int count) {
        List<RepertoireCacheInfo> list = new ArrayList<>();
        
        // 常见剧目名称模板
        String[] templates = {
            "虚无的十字架", "千与千寻", "哈利波特", "狮子王", "猫", "歌剧魅影", 
            "悲惨世界", "音乐之声", "芝加哥", "妈妈咪呀", "西贡小姐", "剧院魅影",
            "红磨坊", "巴黎圣母院", "罗密欧与朱丽叶", "天鹅湖", "胡桃夹子", "睡美人",
            "仲夏夜之梦", "哈姆雷特", "麦克白", "李尔王", "奥赛罗", "威尼斯商人"
        };
        
        String[] prefixes = {"", "经典", "新版", "音乐剧", "话剧", "舞剧", "儿童剧"};
        String[] suffixes = {"", "中文版", "英文版", "音乐剧版", "2024版", "特别版"};
        
        Random random = new Random(42); // 固定种子确保可重复性
        
        for (int i = 0; i < count; i++) {
            String template = templates[random.nextInt(templates.length)];
            String prefix = prefixes[random.nextInt(prefixes.length)];
            String suffix = suffixes[random.nextInt(suffixes.length)];
            
            String name = prefix + template + suffix;
            if (name.length() > 20) {
                name = name.substring(0, 20); // 限制长度
            }
            
            list.add(new RepertoireCacheInfo((long) i, name));
        }
        
        return list;
    }
    
    /**
     * 创建测试查询
     */
    private List<String> createTestQueries() {
        List<String> queries = new ArrayList<>();
        
        // 完全匹配查询
        queries.add("虚无的十字架");
        queries.add("千与千寻");
        queries.add("哈利波特");
        
        // 部分匹配查询
        queries.add("虚无的十字架-东野圭吾沉浸式虐心悬疑话剧《虚无的十字架》[悬疑嘉年华]");
        queries.add("千与千寻日语舞台剧演出票");
        queries.add("哈利波特与魔法石音乐剧");
        
        // 模糊匹配查询
        queries.add("十字架");
        queries.add("千寻");
        queries.add("波特");
        
        // 长文本查询（模拟票面识别结果）
        queries.add("虚无的十字架-东野圭吾沉浸式虐心悬疑话剧《虚无的十字架》[悬疑嘉年华]QUNENRTC2025-07-2619:30|星空间322号1张票￥190.00|一层B区2排1号淘麦VIP");
        queries.add("日语舞台剧《千与千寻》2024年12月演出 上海文化广场 A区5排10号 票价380元");
        
        return queries;
    }
}
